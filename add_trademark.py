#!/usr/bin/env python3
"""
<PERSON>ript to add a trademark symbol to the Vexita logo
"""

from PIL import Image, ImageDraw, ImageFont
import os

def add_trademark_to_logo():
    # Input and output paths
    input_path = "public/vexita_it_logo.png"
    output_path = "public/vexita_it_logo_with_trademark.png"
    
    if not os.path.exists(input_path):
        print(f"Error: {input_path} not found")
        return False
    
    try:
        # Open the original logo
        logo = Image.open(input_path)
        print(f"Original logo size: {logo.size}")
        
        # Create a copy to work with
        logo_with_tm = logo.copy()
        
        # Create a drawing context
        draw = ImageDraw.Draw(logo_with_tm)
        
        # Calculate trademark symbol position and size
        # Position it at the top-right of the logo text area
        logo_width, logo_height = logo.size

        # Try to use a system font, fallback to default if not available
        try:
            # Calculate font size based on logo height (about 18% of logo height for much better visibility)
            font_size = max(int(logo_height * 0.18), 80)
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
        except:
            try:
                font_size = max(int(logo_height * 0.18), 80)
                font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
            except:
                # Fallback to default font
                font = ImageFont.load_default()
                font_size = 80
        
        # Trademark symbol
        tm_symbol = "®"
        
        # Get text dimensions
        bbox = draw.textbbox((0, 0), tm_symbol, font=font)
        tm_width = bbox[2] - bbox[0]
        tm_height = bbox[3] - bbox[1]
        
        # Position the trademark symbol
        # Place it right after the "a" in "vexita" without overlapping
        # The "vexita" text ends around 90% of the logo width, so position it right after
        tm_x = int(logo_width * 0.90) + 10  # Right after the text with small gap to avoid overlap
        tm_y = logo_height - tm_height - int(logo_height * 0.20)  # Align with text baseline

        # Draw the trademark symbol in the same blue color as the logo text (#3982a3)
        # RGB values for #3982a3: (57, 130, 163)
        draw.text((tm_x, tm_y), tm_symbol, fill=(57, 130, 163), font=font)
        
        # Save the result
        logo_with_tm.save(output_path, "PNG")
        print(f"Logo with trademark saved to: {output_path}")
        print(f"Trademark position: ({tm_x}, {tm_y})")
        print(f"Font size used: {font_size}")
        
        return True
        
    except Exception as e:
        print(f"Error processing logo: {e}")
        return False

if __name__ == "__main__":
    success = add_trademark_to_logo()
    if success:
        print("Successfully added trademark symbol to logo!")
    else:
        print("Failed to add trademark symbol to logo.")

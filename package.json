{"name": "vexita", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\""}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@types/nodemailer": "^6.4.17", "i18next": "^25.0.1", "negotiator": "^1.0.0", "next": "^15.3.1", "next-i18next": "^15.4.2", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.1", "server-only": "^0.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^10.1.2", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}